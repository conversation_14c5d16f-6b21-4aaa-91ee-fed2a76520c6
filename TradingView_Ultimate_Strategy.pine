//@version=5
strategy("🚀 ULTIMATE FOREX MASTER 🚀", shorttitle="UFM", overlay=true, 
         default_qty_type=strategy.percent_of_equity, default_qty_value=10,
         commission_type=strategy.commission.percent, commission_value=0.1)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎯 INPUT PARAMETRELERİ
// ═══════════════════════════════════════════════════════════════════════════════════

// Ana Strateji Ayarları
strategy_group = "🎯 ANA STRATEJİ"
use_multi_timeframe = input.bool(true, "Çoklu Zaman Dilimi Analizi", group=strategy_group)
use_ai_signals = input.bool(true, "AI Sinyal Sistemi", group=strategy_group)
use_volume_analysis = input.bool(true, "Hacim Analizi", group=strategy_group)
use_market_structure = input.bool(true, "Piyasa Yapısı Analizi", group=strategy_group)

// Teknik İndikatörler
indicators_group = "📊 TEKNİK İNDİKATÖRLER"
ema_fast = input.int(9, "Hızlı EMA", minval=1, group=indicators_group)
ema_slow = input.int(21, "Yavaş EMA", minval=1, group=indicators_group)
rsi_period = input.int(14, "RSI Periyodu", minval=1, group=indicators_group)
macd_fast = input.int(12, "MACD Hızlı", minval=1, group=indicators_group)
macd_slow = input.int(26, "MACD Yavaş", minval=1, group=indicators_group)
macd_signal = input.int(9, "MACD Sinyal", minval=1, group=indicators_group)
bb_length = input.int(20, "Bollinger Bands", minval=1, group=indicators_group)
bb_mult = input.float(2.0, "BB Çarpanı", minval=0.1, group=indicators_group)

// Risk Yönetimi
risk_group = "⚡ RİSK YÖNETİMİ"
use_dynamic_sl = input.bool(true, "Dinamik Stop Loss", group=risk_group)
atr_period = input.int(14, "ATR Periyodu", minval=1, group=risk_group)
atr_multiplier = input.float(2.0, "ATR Çarpanı", minval=0.1, group=risk_group)
risk_reward_ratio = input.float(2.0, "Risk/Ödül Oranı", minval=0.1, group=risk_group)
max_risk_percent = input.float(2.0, "Maksimum Risk %", minval=0.1, maxval=10, group=risk_group)

// Zaman Filtreleri
time_group = "⏰ ZAMAN FİLTRELERİ"
use_time_filter = input.bool(true, "Zaman Filtresi", group=time_group)
start_hour = input.int(8, "Başlangıç Saati", minval=0, maxval=23, group=time_group)
end_hour = input.int(18, "Bitiş Saati", minval=0, maxval=23, group=time_group)
avoid_news = input.bool(true, "Haber Saatlerinden Kaçın", group=time_group)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🧠 YAPAY ZEKA VE HESAPLAMALAR
// ═══════════════════════════════════════════════════════════════════════════════════

// Çoklu Zaman Dilimi Verileri
htf_timeframe = timeframe.multiplier <= 15 ? "1H" : timeframe.multiplier <= 60 ? "4H" : "1D"
htf_close = request.security(syminfo.tickerid, htf_timeframe, close)
htf_ema_fast = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema_fast))
htf_ema_slow = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema_slow))

// Ana İndikatörler
ema_fast_val = ta.ema(close, ema_fast)
ema_slow_val = ta.ema(close, ema_slow)
rsi_val = ta.rsi(close, rsi_period)
[macd_line, signal_line, _] = ta.macd(close, macd_fast, macd_slow, macd_signal)
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_length, bb_mult)
atr_val = ta.atr(atr_period)

// Hacim Analizi
volume_sma = ta.sma(volume, 20)
volume_ratio = volume / volume_sma
high_volume = volume_ratio > 1.5

// Piyasa Yapısı Analizi
pivot_high = ta.pivothigh(high, 5, 5)
pivot_low = ta.pivotlow(low, 5, 5)
higher_high = pivot_high and pivot_high > pivot_high[1]
lower_low = pivot_low and pivot_low < pivot_low[1]

// Trend Gücü Hesaplama (AI Benzeri)
trend_strength = 0.0
trend_strength := (ema_fast_val > ema_slow_val ? 1 : -1) * 
                  (rsi_val > 50 ? 0.3 : -0.3) * 
                  (macd_line > signal_line ? 0.4 : -0.4) * 
                  (close > bb_middle ? 0.3 : -0.3)

// Momentum Skoru
momentum_score = 0.0
momentum_score := (ta.change(close, 1) > 0 ? 1 : -1) * 
                  (ta.change(close, 5) > 0 ? 1 : -1) * 
                  (ta.change(close, 10) > 0 ? 1 : -1) * 
                  (volume_ratio > 1.2 ? 1.2 : 1.0)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎯 SİNYAL SİSTEMİ
// ═══════════════════════════════════════════════════════════════════════════════════

// Ana Trend Sinyalleri
bullish_trend = ema_fast_val > ema_slow_val and htf_ema_fast > htf_ema_slow
bearish_trend = ema_fast_val < ema_slow_val and htf_ema_fast < htf_ema_slow

// Momentum Sinyalleri
bullish_momentum = rsi_val > 40 and rsi_val < 80 and macd_line > signal_line
bearish_momentum = rsi_val < 60 and rsi_val > 20 and macd_line < signal_line

// Hacim Onayı
volume_confirmation = high_volume or not use_volume_analysis

// Bollinger Bands Sinyalleri
bb_squeeze = (bb_upper - bb_lower) / bb_middle < 0.1
bb_expansion = (bb_upper - bb_lower) / bb_middle > 0.15

// Zaman Filtresi
time_ok = not use_time_filter or (hour >= start_hour and hour <= end_hour)

// Ana Sinyal Hesaplama
buy_signal = bullish_trend and bullish_momentum and volume_confirmation and 
             trend_strength > 0.5 and momentum_score > 0 and time_ok

sell_signal = bearish_trend and bearish_momentum and volume_confirmation and 
              trend_strength < -0.5 and momentum_score < 0 and time_ok

// ═══════════════════════════════════════════════════════════════════════════════════
// 💰 RİSK YÖNETİMİ VE POZİSYON BOYUTU
// ═══════════════════════════════════════════════════════════════════════════════════

// Dinamik Stop Loss
dynamic_sl_distance = use_dynamic_sl ? atr_val * atr_multiplier : close * 0.02

// Pozisyon Boyutu Hesaplama
account_risk = strategy.equity * (max_risk_percent / 100)
position_size = account_risk / dynamic_sl_distance

// ═══════════════════════════════════════════════════════════════════════════════════
// 📈 STRATEJİ EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════

// Long Pozisyon
if buy_signal and strategy.position_size == 0
    sl_price = close - dynamic_sl_distance
    tp_price = close + (dynamic_sl_distance * risk_reward_ratio)
    strategy.entry("LONG", strategy.long, comment="🚀 LONG")
    strategy.exit("LONG_EXIT", "LONG", stop=sl_price, limit=tp_price, comment="📊 LONG EXIT")

// Short Pozisyon  
if sell_signal and strategy.position_size == 0
    sl_price = close + dynamic_sl_distance
    tp_price = close - (dynamic_sl_distance * risk_reward_ratio)
    strategy.entry("SHORT", strategy.short, comment="🔻 SHORT")
    strategy.exit("SHORT_EXIT", "SHORT", stop=sl_price, limit=tp_price, comment="📊 SHORT EXIT")

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎨 GÖRSELLEŞTİRME
// ═══════════════════════════════════════════════════════════════════════════════════

// EMA Çizgileri
plot(ema_fast_val, "EMA Hızlı", color=color.blue, linewidth=2)
plot(ema_slow_val, "EMA Yavaş", color=color.red, linewidth=2)

// Bollinger Bands
p1 = plot(bb_upper, "BB Üst", color=color.gray)
p2 = plot(bb_lower, "BB Alt", color=color.gray)
fill(p1, p2, color=color.new(color.blue, 95))

// Sinyal Okları
plotshape(buy_signal, "BUY", shape.triangleup, location.belowbar, 
          color=color.green, size=size.normal)
plotshape(sell_signal, "SELL", shape.triangledown, location.abovebar, 
          color=color.red, size=size.normal)

// Trend Gücü Göstergesi
bgcolor(trend_strength > 0.7 ? color.new(color.green, 90) :
        trend_strength < -0.7 ? color.new(color.red, 90) : na)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🔥 GELİŞMİŞ ÖZELLİKLER
// ═══════════════════════════════════════════════════════════════════════════════════

// Fibonacci Seviyeleri
var float fib_high = na
var float fib_low = na
if ta.change(ta.highest(high, 50)) != 0
    fib_high := ta.highest(high, 50)
if ta.change(ta.lowest(low, 50)) != 0
    fib_low := ta.lowest(low, 50)

fib_diff = fib_high - fib_low
fib_236 = fib_high - (fib_diff * 0.236)
fib_382 = fib_high - (fib_diff * 0.382)
fib_618 = fib_high - (fib_diff * 0.618)

// Support/Resistance Seviyeleri
resistance_level = ta.highest(high, 20)
support_level = ta.lowest(low, 20)

// Market Sentiment Analizi
bullish_candles = close > open ? 1 : 0
bearish_candles = close < open ? 1 : 0
sentiment_score = ta.sma(bullish_candles - bearish_candles, 10) * 100

// Volatilite Analizi
volatility = ta.stdev(ta.change(close), 20)
high_volatility = volatility > ta.sma(volatility, 50) * 1.5

// Smart Money Concepts
order_block_bull = low[1] > low[2] and low > low[1] and close > high[1]
order_block_bear = high[1] < high[2] and high < high[1] and close < low[1]

// Liquidity Zones
equal_highs = math.abs(high - high[1]) < atr_val * 0.1
equal_lows = math.abs(low - low[1]) < atr_val * 0.1

// ═══════════════════════════════════════════════════════════════════════════════════
// 📊 DASHBOARD VE BİLGİ PANELİ
// ═══════════════════════════════════════════════════════════════════════════════════

// Dashboard Tablosu
var table dashboard = table.new(position.top_right, 3, 15, bgcolor=color.white, border_width=1)

if barstate.islast
    // Başlık
    table.cell(dashboard, 0, 0, "🚀 ULTIMATE FOREX MASTER", text_color=color.white, bgcolor=color.blue, text_size=size.normal)
    table.merge_cells(dashboard, 0, 0, 2, 0)

    // Trend Bilgisi
    trend_text = trend_strength > 0.5 ? "🟢 GÜÇLÜ YUKARI" : trend_strength < -0.5 ? "🔴 GÜÇLÜ AŞAĞI" : "🟡 YAN TREND"
    table.cell(dashboard, 0, 1, "Trend:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 1, trend_text, text_color=color.black, bgcolor=color.white)

    // RSI Bilgisi
    rsi_text = rsi_val > 70 ? "🔴 AŞIRI ALIM" : rsi_val < 30 ? "🟢 AŞIRI SATIM" : "🟡 NORMAL"
    table.cell(dashboard, 0, 2, "RSI:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 2, str.tostring(math.round(rsi_val, 1)) + " " + rsi_text, text_color=color.black, bgcolor=color.white)

    // MACD Bilgisi
    macd_text = macd_line > signal_line ? "🟢 YUKARI" : "🔴 AŞAĞI"
    table.cell(dashboard, 0, 3, "MACD:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 3, macd_text, text_color=color.black, bgcolor=color.white)

    // Hacim Bilgisi
    volume_text = volume_ratio > 1.5 ? "🟢 YÜKSEK" : volume_ratio < 0.8 ? "🔴 DÜŞÜK" : "🟡 NORMAL"
    table.cell(dashboard, 0, 4, "Hacim:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 4, str.tostring(math.round(volume_ratio, 2)) + " " + volume_text, text_color=color.black, bgcolor=color.white)

    // Volatilite Bilgisi
    vol_text = high_volatility ? "🔴 YÜKSEK" : "🟢 NORMAL"
    table.cell(dashboard, 0, 5, "Volatilite:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 5, vol_text, text_color=color.black, bgcolor=color.white)

    // Sentiment Skoru
    sentiment_text = sentiment_score > 20 ? "🟢 BOĞA" : sentiment_score < -20 ? "🔴 AYI" : "🟡 NÖTR"
    table.cell(dashboard, 0, 6, "Sentiment:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 6, str.tostring(math.round(sentiment_score, 1)) + " " + sentiment_text, text_color=color.black, bgcolor=color.white)

    // Pozisyon Bilgisi
    pos_text = strategy.position_size > 0 ? "🟢 LONG" : strategy.position_size < 0 ? "🔴 SHORT" : "⚪ YOK"
    table.cell(dashboard, 0, 7, "Pozisyon:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 7, pos_text, text_color=color.black, bgcolor=color.white)

    // P&L Bilgisi
    pnl_color = strategy.netprofit > 0 ? color.green : strategy.netprofit < 0 ? color.red : color.gray
    table.cell(dashboard, 0, 8, "P&L:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 8, str.tostring(math.round(strategy.netprofit, 2)), text_color=pnl_color, bgcolor=color.white)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎯 GELİŞMİŞ GÖRSELLEŞTİRME
// ═══════════════════════════════════════════════════════════════════════════════════

// Fibonacci Seviyeleri
plot(fib_236, "Fib 23.6%", color=color.yellow, linewidth=1, style=plot.style_line)
plot(fib_382, "Fib 38.2%", color=color.orange, linewidth=1, style=plot.style_line)
plot(fib_618, "Fib 61.8%", color=color.purple, linewidth=1, style=plot.style_line)

// Support/Resistance
plot(resistance_level, "Direnç", color=color.red, linewidth=2, style=plot.style_stepline)
plot(support_level, "Destek", color=color.green, linewidth=2, style=plot.style_stepline)

// Order Blocks
plotshape(order_block_bull, "Bull OB", shape.square, location.belowbar, color=color.green, size=size.small)
plotshape(order_block_bear, "Bear OB", shape.square, location.abovebar, color=color.red, size=size.small)

// Liquidity Zones
plotshape(equal_highs, "EQH", shape.diamond, location.abovebar, color=color.yellow, size=size.tiny)
plotshape(equal_lows, "EQL", shape.diamond, location.belowbar, color=color.yellow, size=size.tiny)

// Trend Değişim Uyarıları
alertcondition(ta.crossover(ema_fast_val, ema_slow_val), "Bullish Crossover", "🚀 Boğa Trendi Başlıyor!")
alertcondition(ta.crossunder(ema_fast_val, ema_slow_val), "Bearish Crossover", "🔻 Ayı Trendi Başlıyor!")
alertcondition(buy_signal, "Buy Signal", "💰 SATIN AL SİNYALİ!")
alertcondition(sell_signal, "Sell Signal", "💸 SAT SİNYALİ!")
