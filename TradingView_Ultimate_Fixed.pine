//@version=6
strategy("🌟 QUANTUM AI TRADING MASTER v6 🌟", shorttitle="QATM", overlay=true, 
         default_qty_type=strategy.percent_of_equity, default_qty_value=5,
         commission_type=strategy.commission.percent, commission_value=0.1)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎯 INPUT PARAMETRELERİ
// ═══════════════════════════════════════════════════════════════════════════════════

// Ana Strateji Ayarları
strategy_group = "🎯 QUANTUM AI STRATEJİ"
use_multi_timeframe = input.bool(true, "Çoklu Zaman Dilimi Analizi", group=strategy_group)
use_ai_signals = input.bool(true, "AI Sinyal Sistemi", group=strategy_group)
use_volume_analysis = input.bool(true, "Hacim Analizi", group=strategy_group)
use_market_structure = input.bool(true, "Piyasa Yapısı Analizi", group=strategy_group)

// Teknik İndikatörler
indicators_group = "📊 QUANTUM İNDİKATÖRLER"
ema_fast = input.int(9, "Hızlı EMA", minval=1, group=indicators_group)
ema_slow = input.int(21, "Yavaş EMA", minval=1, group=indicators_group)
ema_ultra = input.int(50, "Ultra EMA", minval=1, group=indicators_group)
rsi_period = input.int(14, "RSI Periyodu", minval=1, group=indicators_group)
macd_fast = input.int(12, "MACD Hızlı", minval=1, group=indicators_group)
macd_slow = input.int(26, "MACD Yavaş", minval=1, group=indicators_group)
macd_signal = input.int(9, "MACD Sinyal", minval=1, group=indicators_group)
bb_length = input.int(20, "Bollinger Bands", minval=1, group=indicators_group)
bb_mult = input.float(2.0, "BB Çarpanı", minval=0.1, group=indicators_group)
stoch_k = input.int(14, "Stochastic %K", minval=1, group=indicators_group)
stoch_d = input.int(3, "Stochastic %D", minval=1, group=indicators_group)
adx_period = input.int(14, "ADX Periyodu", minval=1, group=indicators_group)

// Risk Yönetimi
risk_group = "⚡ QUANTUM RİSK YÖNETİMİ"
use_dynamic_sl = input.bool(true, "Dinamik Stop Loss", group=risk_group)
use_trailing_sl = input.bool(true, "Trailing Stop Loss", group=risk_group)
use_partial_tp = input.bool(true, "Kısmi Kar Alma", group=risk_group)
atr_period = input.int(14, "ATR Periyodu", minval=1, group=risk_group)
atr_multiplier = input.float(2.0, "ATR Çarpanı", minval=0.1, group=risk_group)
risk_reward_ratio = input.float(3.0, "Risk/Ödül Oranı", minval=0.1, group=risk_group)
max_risk_percent = input.float(1.5, "Maksimum Risk %", minval=0.1, maxval=10, group=risk_group)

// Zaman Filtreleri
time_group = "⏰ ZAMAN FİLTRELERİ"
use_time_filter = input.bool(true, "Zaman Filtresi", group=time_group)
start_hour = input.int(8, "Başlangıç Saati", minval=0, maxval=23, group=time_group)
end_hour = input.int(18, "Bitiş Saati", minval=0, maxval=23, group=time_group)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🧠 QUANTUM AI VE HESAPLAMALAR
// ═══════════════════════════════════════════════════════════════════════════════════

// Çoklu Zaman Dilimi Verileri
htf_timeframe = timeframe.multiplier <= 15 ? "1H" : timeframe.multiplier <= 60 ? "4H" : "1D"
htf_ema_fast = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema_fast), lookahead=barmerge.lookahead_off)
htf_ema_slow = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema_slow), lookahead=barmerge.lookahead_off)

// Ana İndikatörler
ema_fast_val = ta.ema(close, ema_fast)
ema_slow_val = ta.ema(close, ema_slow)
ema_ultra_val = ta.ema(close, ema_ultra)
rsi_val = ta.rsi(close, rsi_period)
[macd_line, signal_line, macd_histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_length, bb_mult)
atr_val = ta.atr(atr_period)

// Gelişmiş İndikatörler
stoch_k_val = ta.stoch(close, high, low, stoch_k)
stoch_d_val = ta.sma(stoch_k_val, stoch_d)
adx_val = ta.adx(adx_period)
vwap_val = ta.vwap

// Hacim Analizi
volume_sma = ta.sma(volume, 20)
volume_ratio = volume / math.max(volume_sma, 1)
high_volume = volume_ratio > 1.5

// AI Neural Network Simulation
tanh_approx(x) => x / (1 + math.abs(x))
neural_input_1 = (close - ta.sma(close, 20)) / math.max(ta.stdev(close, 20), 0.0001)
neural_input_2 = (rsi_val - 50) / 50
neural_input_3 = (macd_line - signal_line) / math.max(ta.stdev(macd_line, 20), 0.0001)

hidden_1 = tanh_approx(neural_input_1 * 0.5 + neural_input_2 * 0.3 + neural_input_3 * 0.2)
hidden_2 = tanh_approx(neural_input_2 * 0.4 + neural_input_3 * 0.4)
ai_prediction = tanh_approx(hidden_1 * 0.6 + hidden_2 * 0.4)

// Quantum Computing Simulation
quantum_superposition = math.cos(close * 0.01) + math.sin(volume * 0.001)
quantum_interference = math.cos(rsi_val * 0.1) * math.sin(macd_line * 0.1)
var float quantum_entanglement = 0.0
quantum_entanglement := quantum_superposition * quantum_interference * 0.1 + quantum_entanglement * 0.9

// Advanced Trend Strength
trend_strength = (ema_fast_val > ema_slow_val ? 1 : -1) * 
                 (ema_slow_val > ema_ultra_val ? 0.3 : -0.3) * 
                 (rsi_val > 50 ? 0.2 : -0.2) * 
                 (macd_line > signal_line ? 0.3 : -0.3) * 
                 (close > bb_middle ? 0.2 : -0.2) * 
                 (adx_val > 25 ? 0.3 : 0.1) * 
                 (close > vwap_val ? 0.2 : -0.2) + 
                 ai_prediction * 0.4 + 
                 quantum_entanglement * 0.3

// Momentum Score
momentum_score = (ta.change(close, 1) > 0 ? 1 : -1) * 
                 (ta.change(close, 5) > 0 ? 1 : -1) * 
                 (ta.change(close, 10) > 0 ? 1 : -1) * 
                 (volume_ratio > 1.2 ? 1.2 : 1.0) * 
                 (stoch_k_val > stoch_d_val ? 1.1 : 0.9) * 
                 ai_prediction * 1.5

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎯 QUANTUM SİNYAL SİSTEMİ
// ═══════════════════════════════════════════════════════════════════════════════════

// Trend Sinyalleri
bullish_trend_1 = ema_fast_val > ema_slow_val
bullish_trend_2 = ema_slow_val > ema_ultra_val
bullish_trend_3 = htf_ema_fast > htf_ema_slow
bullish_trend_4 = close > vwap_val
bullish_trend = bullish_trend_1 and bullish_trend_2 and bullish_trend_3 and bullish_trend_4

bearish_trend_1 = ema_fast_val < ema_slow_val
bearish_trend_2 = ema_slow_val < ema_ultra_val
bearish_trend_3 = htf_ema_fast < htf_ema_slow
bearish_trend_4 = close < vwap_val
bearish_trend = bearish_trend_1 and bearish_trend_2 and bearish_trend_3 and bearish_trend_4

// Momentum Sinyalleri
bullish_momentum_1 = rsi_val > 40 and rsi_val < 80
bullish_momentum_2 = macd_line > signal_line
bullish_momentum_3 = stoch_k_val > stoch_d_val
bullish_momentum_4 = adx_val > 25
bullish_momentum = bullish_momentum_1 and bullish_momentum_2 and bullish_momentum_3 and bullish_momentum_4

bearish_momentum_1 = rsi_val < 60 and rsi_val > 20
bearish_momentum_2 = macd_line < signal_line
bearish_momentum_3 = stoch_k_val < stoch_d_val
bearish_momentum_4 = adx_val > 25
bearish_momentum = bearish_momentum_1 and bearish_momentum_2 and bearish_momentum_3 and bearish_momentum_4

// AI Confirmation
ai_bullish = ai_prediction > 0.3 and quantum_entanglement > 0
ai_bearish = ai_prediction < -0.3 and quantum_entanglement < 0

// Volume Confirmation
volume_bullish = high_volume and volume_ratio > 1.3
volume_bearish = high_volume and volume_ratio > 1.3

// Bollinger Bands
bb_bullish = close > bb_middle and (bb_upper - bb_lower) / bb_middle > 0.15
bb_bearish = close < bb_middle and (bb_upper - bb_lower) / bb_middle > 0.15

// Zaman Filtresi
time_ok = not use_time_filter or (hour >= start_hour and hour <= end_hour)

// QUANTUM SIGNAL CALCULATION
quantum_buy_score = (bullish_trend ? 3.0 : 0) + 
                    (bullish_momentum ? 2.0 : 0) + 
                    (ai_bullish ? 2.0 : 0) + 
                    (volume_bullish ? 1.0 : 0) + 
                    (bb_bullish ? 1.0 : 0) + 
                    (trend_strength > 0.7 ? 1.0 : 0)

quantum_sell_score = (bearish_trend ? 3.0 : 0) + 
                     (bearish_momentum ? 2.0 : 0) + 
                     (ai_bearish ? 2.0 : 0) + 
                     (volume_bearish ? 1.0 : 0) + 
                     (bb_bearish ? 1.0 : 0) + 
                     (trend_strength < -0.7 ? 1.0 : 0)

// Final Signal Decision
buy_signal_1 = quantum_buy_score >= 7.0
buy_signal_2 = time_ok
buy_signal_3 = quantum_sell_score < 3.0
buy_signal = buy_signal_1 and buy_signal_2 and buy_signal_3

sell_signal_1 = quantum_sell_score >= 7.0
sell_signal_2 = time_ok
sell_signal_3 = quantum_buy_score < 3.0
sell_signal = sell_signal_1 and sell_signal_2 and sell_signal_3

// ═══════════════════════════════════════════════════════════════════════════════════
// 💰 RİSK YÖNETİMİ
// ═══════════════════════════════════════════════════════════════════════════════════

// Dynamic Stop Loss
volatility_factor = atr_val / math.max(ta.sma(atr_val, 20), 0.0001)
dynamic_sl_distance = use_dynamic_sl ? atr_val * atr_multiplier * volatility_factor : close * 0.02

// Position Sizing
base_position_size = strategy.equity * (max_risk_percent / 100) / math.max(dynamic_sl_distance, 0.0001)
ai_confidence = math.abs(ai_prediction)
confidence_multiplier = 0.5 + (ai_confidence * 1.5)
position_size = base_position_size * confidence_multiplier

// Take Profit Levels
tp_level_1 = dynamic_sl_distance * risk_reward_ratio * 0.5
tp_level_2 = dynamic_sl_distance * risk_reward_ratio * 1.0
tp_level_3 = dynamic_sl_distance * risk_reward_ratio * 1.5

// ═══════════════════════════════════════════════════════════════════════════════════
// 📈 STRATEJİ EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════

// Long Position Management
if buy_signal and strategy.position_size == 0
    entry_price = close
    sl_price = entry_price - dynamic_sl_distance
    tp1_price = entry_price + tp_level_1
    tp2_price = entry_price + tp_level_2
    tp3_price = entry_price + tp_level_3
    
    strategy.entry("LONG", strategy.long, qty=position_size, comment="🚀 QUANTUM LONG")
    
    if use_partial_tp
        strategy.exit("TP1", "LONG", qty_percent=33, limit=tp1_price, stop=sl_price, comment="📊 TP1")
        strategy.exit("TP2", "LONG", qty_percent=33, limit=tp2_price, stop=sl_price, comment="📊 TP2")
        strategy.exit("TP3", "LONG", qty_percent=34, limit=tp3_price, stop=sl_price, comment="📊 TP3")
    else
        strategy.exit("LONG_EXIT", "LONG", stop=sl_price, limit=tp2_price, comment="📊 LONG EXIT")

// Short Position Management
if sell_signal and strategy.position_size == 0
    entry_price = close
    sl_price = entry_price + dynamic_sl_distance
    tp1_price = entry_price - tp_level_1
    tp2_price = entry_price - tp_level_2
    tp3_price = entry_price - tp_level_3
    
    strategy.entry("SHORT", strategy.short, qty=position_size, comment="🔻 QUANTUM SHORT")
    
    if use_partial_tp
        strategy.exit("TP1", "SHORT", qty_percent=33, limit=tp1_price, stop=sl_price, comment="📊 TP1")
        strategy.exit("TP2", "SHORT", qty_percent=33, limit=tp2_price, stop=sl_price, comment="📊 TP2")
        strategy.exit("TP3", "SHORT", qty_percent=34, limit=tp3_price, stop=sl_price, comment="📊 TP3")
    else
        strategy.exit("SHORT_EXIT", "SHORT", stop=sl_price, limit=tp2_price, comment="📊 SHORT EXIT")

// Emergency Exit
emergency_exit_long = strategy.position_size > 0 and quantum_sell_score > 8.0
emergency_exit_short = strategy.position_size < 0 and quantum_buy_score > 8.0

if emergency_exit_long
    strategy.close("LONG", comment="🚨 EMERGENCY EXIT LONG")
if emergency_exit_short
    strategy.close("SHORT", comment="🚨 EMERGENCY EXIT SHORT")

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎨 GÖRSELLEŞTİRME
// ═══════════════════════════════════════════════════════════════════════════════════

// EMA System
plot(ema_fast_val, "EMA Hızlı", color=color.blue, linewidth=2)
plot(ema_slow_val, "EMA Yavaş", color=color.red, linewidth=2)
plot(ema_ultra_val, "EMA Ultra", color=color.purple, linewidth=2)
plot(vwap_val, "VWAP", color=color.orange, linewidth=2)

// Bollinger Bands
p1 = plot(bb_upper, "BB Üst", color=color.gray, linewidth=1)
p2 = plot(bb_lower, "BB Alt", color=color.gray, linewidth=1)
p3 = plot(bb_middle, "BB Orta", color=color.yellow, linewidth=1)
fill(p1, p2, color=color.new(color.blue, 95))

// Quantum Signals
plotshape(buy_signal, "🚀 QUANTUM BUY", shape.triangleup, location.belowbar,
          color=color.lime, size=size.large, text="🚀")
plotshape(sell_signal, "🔻 QUANTUM SELL", shape.triangledown, location.abovebar,
          color=color.red, size=size.large, text="🔻")

// Trend Strength Background
bgcolor(trend_strength > 0.8 ? color.new(color.green, 85) :
        trend_strength < -0.8 ? color.new(color.red, 85) :
        trend_strength > 0.5 ? color.new(color.green, 95) :
        trend_strength < -0.5 ? color.new(color.red, 95) : na)

// ═══════════════════════════════════════════════════════════════════════════════════
// 📊 DASHBOARD
// ═══════════════════════════════════════════════════════════════════════════════════

var table dashboard = table.new(position.top_right, 3, 15, bgcolor=color.white, border_width=2)

if barstate.islast
    // Başlık
    table.cell(dashboard, 0, 0, "🌟 QUANTUM AI MASTER 🌟", text_color=color.white, bgcolor=color.navy, text_size=size.large)
    table.merge_cells(dashboard, 0, 0, 2, 0)

    // Trend Bilgisi
    trend_text = trend_strength > 0.5 ? "🟢 GÜÇLÜ YUKARI" : trend_strength < -0.5 ? "🔴 GÜÇLÜ AŞAĞI" : "🟡 YAN TREND"
    table.cell(dashboard, 0, 1, "Trend:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 1, trend_text, text_color=color.black, bgcolor=color.white)

    // RSI Bilgisi
    rsi_text = rsi_val > 70 ? "🔴 AŞIRI ALIM" : rsi_val < 30 ? "🟢 AŞIRI SATIM" : "🟡 NORMAL"
    table.cell(dashboard, 0, 2, "RSI:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 2, str.tostring(rsi_val, "#.#") + " " + rsi_text, text_color=color.black, bgcolor=color.white)

    // MACD Bilgisi
    macd_text = macd_line > signal_line ? "🟢 YUKARI" : "🔴 AŞAĞI"
    table.cell(dashboard, 0, 3, "MACD:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 3, macd_text, text_color=color.black, bgcolor=color.white)

    // Hacim Bilgisi
    volume_text = volume_ratio > 1.5 ? "🟢 YÜKSEK" : volume_ratio < 0.8 ? "🔴 DÜŞÜK" : "🟡 NORMAL"
    table.cell(dashboard, 0, 4, "Hacim:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 4, str.tostring(volume_ratio, "#.##") + " " + volume_text, text_color=color.black, bgcolor=color.white)

    // AI Prediction
    ai_text = ai_prediction > 0.3 ? "🟢 YUKARI" : ai_prediction < -0.3 ? "🔴 AŞAĞI" : "🟡 NÖTR"
    table.cell(dashboard, 0, 5, "AI Tahmin:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 5, str.tostring(ai_prediction, "#.###") + " " + ai_text, text_color=color.black, bgcolor=color.white)

    // Quantum Entanglement
    quantum_text = quantum_entanglement > 0 ? "🟢 POZİTİF" : "🔴 NEGATİF"
    table.cell(dashboard, 0, 6, "Quantum:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 6, str.tostring(quantum_entanglement, "#.###") + " " + quantum_text, text_color=color.black, bgcolor=color.white)

    // Pozisyon Bilgisi
    pos_text = strategy.position_size > 0 ? "🟢 LONG" : strategy.position_size < 0 ? "🔴 SHORT" : "⚪ YOK"
    table.cell(dashboard, 0, 7, "Pozisyon:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 7, pos_text, text_color=color.black, bgcolor=color.white)

    // P&L Bilgisi
    pnl_color = strategy.netprofit > 0 ? color.green : strategy.netprofit < 0 ? color.red : color.gray
    table.cell(dashboard, 0, 8, "P&L:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 8, str.tostring(strategy.netprofit, "#.##"), text_color=pnl_color, bgcolor=color.white)

    // Buy Score
    table.cell(dashboard, 0, 9, "Buy Score:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 9, str.tostring(quantum_buy_score, "#.#") + "/10", text_color=color.green, bgcolor=color.white)

    // Sell Score
    table.cell(dashboard, 0, 10, "Sell Score:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 10, str.tostring(quantum_sell_score, "#.#") + "/10", text_color=color.red, bgcolor=color.white)

    // Position Size
    table.cell(dashboard, 0, 11, "Pos Size:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 11, str.tostring(position_size, "#.####"), text_color=color.blue, bgcolor=color.white)

    // Market Regime
    regime_text = adx_val > 25 ? "🟢 TREND" : "🟡 YAN"
    table.cell(dashboard, 0, 12, "Rejim:", text_color=color.black, bgcolor=color.white)
    table.cell(dashboard, 1, 12, regime_text, text_color=color.black, bgcolor=color.white)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🔔 ALERT SİSTEMİ
// ═══════════════════════════════════════════════════════════════════════════════════

// Ana Sinyal Uyarıları
alertcondition(buy_signal, "Quantum Buy Signal", "🌟 QUANTUM SATIN AL SİNYALİ!")
alertcondition(sell_signal, "Quantum Sell Signal", "🌟 QUANTUM SAT SİNYALİ!")

// AI Uyarıları
alertcondition(ai_prediction > 0.5, "Strong AI Bullish", "🤖 GÜÇLÜ AI BOĞA SİNYALİ!")
alertcondition(ai_prediction < -0.5, "Strong AI Bearish", "🤖 GÜÇLÜ AI AYI SİNYALİ!")

// Risk Uyarıları
alertcondition(emergency_exit_long or emergency_exit_short, "Emergency Exit", "🚨 ACİL ÇIKIŞ SİNYALİ!")

// Trend Değişim Uyarıları
alertcondition(ta.crossover(ema_fast_val, ema_slow_val), "Bullish Crossover", "🚀 Boğa Trendi Başlıyor!")
alertcondition(ta.crossunder(ema_fast_val, ema_slow_val), "Bearish Crossover", "🔻 Ayı Trendi Başlıyor!")

// Market Regime Değişimi
alertcondition(ta.crossover(adx_val, 25), "Trend Market", "📈 Trend Piyasası Başlıyor!")
alertcondition(ta.crossunder(adx_val, 25), "Sideways Market", "📊 Yan Piyasa Başlıyor!")
